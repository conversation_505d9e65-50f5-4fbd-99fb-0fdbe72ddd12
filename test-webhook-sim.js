// Test webhook simulation endpoint
const testWebhookSimulation = async () => {
  try {
    const webhookData = {
      user_id: 'ba40622d-eaae-4437-838a-28d3910360b4',
      profile_id: 'ba40622d-eaae-4437-838a-28d3910360b4',
      amount: 1000, // $10.00 in cents
      event_type: 'payment.succeeded'
    };

    console.log('Testing webhook simulation with data:', webhookData);

    const response = await fetch('http://localhost:5174/api/payments/test/webhook', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(webhookData)
    });

    console.log('Response status:', response.status);
    console.log('Response headers:', Object.fromEntries(response.headers.entries()));

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Error response:', errorText);
      return;
    }

    const result = await response.json();
    console.log('Success response:', JSON.stringify(result, null, 2));

  } catch (error) {
    console.error('Request failed:', error);
  }
};

testWebhookSimulation();
