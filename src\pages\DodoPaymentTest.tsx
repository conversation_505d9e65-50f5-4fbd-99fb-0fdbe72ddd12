import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { PaymentsAPI, SubscriptionsAPI, CustomersAPI } from '@/integrations/dodo-payments/api';
import { useProfileStore } from '@/stores/profileStore';
import { DodoPayments } from 'dodopayments-checkout';
import { supabase } from '@/integrations/supabase/client';

// Interface for API error handling
interface ApiError {
  message?: string;
  error?: string;
}

// Consolidated Dodo Payments Test Page
export default function DodoPaymentTest() {
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<string>('');
  const [amount, setAmount] = useState('10.00');
  const [currency, setCurrency] = useState('USD');
  const [customerId, setCustomerId] = useState('');
  const [email, setEmail] = useState('');
  const [name, setName] = useState('');
  const [planId, setPlanId] = useState('');
  const [interval, setInterval] = useState<'monthly' | 'yearly'>('monthly');
  const { profile } = useProfileStore();

  // Initialize Dodo Payments SDK
  React.useEffect(() => {
    try {
      DodoPayments.Initialize({
        mode: (import.meta.env.VITE_DODO_PAYMENTS_MODE || 'test') as 'test' | 'live',
        onEvent: (event) => {
          console.log('Checkout event:', event);
          switch (event.event_type) {
            case 'checkout.payment_succeeded':
              setResult(`✅ SDK Payment successful!\n\nEvent: ${event.event_type}\nData: ${JSON.stringify(event.data, null, 2)}`);
              setIsLoading(false);
              break;
            case 'checkout.payment_failed':
              setResult(`❌ SDK Payment failed: ${event.data?.message || 'Unknown error'}`);
              setIsLoading(false);
              break;
            case 'checkout.closed':
              if (isLoading) {
                setResult('⚠️ SDK Payment cancelled by user');
                setIsLoading(false);
              }
              break;
            case 'error':
              setResult(`❌ SDK Error: ${event.data?.message || 'Unknown error occurred'}`);
              setIsLoading(false);
              break;
          }
        },
        theme: 'light',
        linkType: 'static',
        displayType: 'overlay'
      });
    } catch (error) {
      console.error('Failed to initialize Dodo Payments SDK:', error);
    }
  }, [isLoading]);

  // Test API-based payment (server-side)
  const handleAPIPayment = async () => {
    setIsLoading(true);
    setResult('');
    
    try {
      const paymentData = {
        billing_currency: currency,
        allowed_payment_method_types: ['credit', 'debit'],
        product_cart: [
          {
            product_id: 'pdt_iSJretaAUZA4a1ZeIBjMQ',
            quantity: 1
          }
        ],
        return_url: `${window.location.origin}/payment-success`,
        customer: customerId ? { customer_id: customerId } : {
          email: email || '<EMAIL>',
          name: name || 'Test User'
        },
        metadata: {
          test: 'true',
          user_id: profile?.id || 'anonymous',
          payment_type: 'api_direct',
          timestamp: new Date().toISOString()
        }
      };

      const response = await fetch('/api/payments/test/checkout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(paymentData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create checkout session');
      }

      const session = await response.json();
      
      setResult(`✅ API Checkout session created!\n\nSession ID: ${session.payment_id || session.id}\nCheckout URL: ${session.payment_link || session.url}\n\nRedirecting to checkout...`);

      if (session.payment_link || session.url) {
        setTimeout(() => {
          window.location.href = session.payment_link || session.url;
        }, 2000);
      }
    } catch (error) {
      console.error('API Payment error:', error);
      setResult(`❌ API Error: ${error instanceof Error ? error.message : 'Unknown error occurred'}`);
    } finally {
      setIsLoading(false);
    }
  };

  // Test simple payment endpoint (similar to Python backend)
  const handleTestEndpoint = async () => {
    setIsLoading(true);
    setResult('');
    
    try {
      const response = await fetch('/api/payments/test/simple-payment', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Test endpoint failed');
      }

      const session = await response.json();
      
      setResult(`✅ Test endpoint successful!\n\nPayment ID: ${session.payment_id || session.id}\nStatus: ${session.status}\nCheckout URL: ${session.payment_link || session.url}`);
      
    } catch (error) {
      console.error('Test endpoint error:', error);
      setResult(`❌ Test endpoint failed: ${error instanceof Error ? error.message : 'Unknown error occurred'}`);
    } finally {
      setIsLoading(false);
    }
  };

  // Test SDK-based payment (client-side)
  const handleSDKPayment = async () => {
    setIsLoading(true);
    setResult('');
    
    try {
      const checkoutOptions = {
        products: [
          {
            productId: 'pdt_iSJretaAUZA4a1ZeIBjMQ',
            quantity: 1
          }
        ],
        redirectUrl: `${window.location.origin}/payment-success`,
        queryParams: {
          amount: amount,
          currency: currency,
          customerId: customerId || '',
          test: 'true',
          user_id: profile?.id || 'anonymous',
          payment_type: 'sdk_direct'
        }
      };
      
      DodoPayments.Checkout.open(checkoutOptions);
      setResult('🔄 SDK Checkout opened...');
    } catch (error) {
      console.error('SDK Payment error:', error);
      setResult(`❌ SDK Error: ${error instanceof Error ? error.message : 'Unknown error occurred'}`);
      setIsLoading(false);
    }
  };

  // Test subscription payment
  const handleSubscriptionPayment = async () => {
    setIsLoading(true);
    setResult('');
    
    try {
      const subscriptionAmount = interval === 'monthly' ? 29.99 : 299.99;
      
      const paymentData = {
        billing_currency: currency,
        allowed_payment_method_types: ['credit', 'debit'],
        product_cart: [
          {
            product_id: planId || `test_${interval}_plan`,
            quantity: 1
          }
        ],
        return_url: `${window.location.origin}/subscription-success`,
        customer: customerId ? { customer_id: customerId } : {
          email: email || '<EMAIL>',
          name: name || 'Test User'
        },
        metadata: {
          test: 'true',
          user_id: profile?.id || 'anonymous',
          payment_type: 'subscription',
          billing_interval: interval,
          plan_id: planId || `test_${interval}_plan`
        }
      };

      const response = await fetch('/api/payments/test/checkout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(paymentData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create subscription checkout');
      }

      const session = await response.json();
      
      setResult(`✅ Subscription checkout created!\n\nSession ID: ${session.subscription_id || session.id}\nBilling: ${interval}\nAmount: $${subscriptionAmount}\n\nRedirecting...`);

      if (session.payment_link || session.url) {
        setTimeout(() => {
          window.location.href = session.payment_link || session.url;
        }, 2000);
      }
    } catch (error) {
      console.error('Subscription error:', error);
      setResult(`❌ Subscription Error: ${error instanceof Error ? error.message : 'Unknown error occurred'}`);
    } finally {
      setIsLoading(false);
    }
  };

  // Direct subscription to the test product (Premium Plan)
  const handleDirectSubscribe = async () => {
    setIsLoading(true);
    setResult('');

    try {
      // Use the actual test product ID that we know works
      const response = await fetch('/api/payments/test/checkout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          user_id: profile?.id,
          profile_id: profile?.id,
          user_email: profile?.email || '<EMAIL>',
          user_name: profile?.username || profile?.email?.split('@')[0] || 'Test User'
        })
      });

      if (!response.ok) {
        console.error('Response not OK:', response.status, response.statusText);
        let errorData;
        try {
          errorData = await response.json();
        } catch (jsonError) {
          console.error('Failed to parse error response as JSON:', jsonError);
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        throw new Error(errorData.error || 'Failed to create subscription checkout');
      }

      let session;
      try {
        session = await response.json();
      } catch (jsonError) {
        console.error('Failed to parse success response as JSON:', jsonError);
        throw new Error('Invalid response format from server');
      }

      // Debug: Log the full session response
      console.log('Full session response:', session);
      console.log('payment_link:', session.payment_link);
      console.log('checkout_url:', session.checkout_url);
      console.log('url:', session.url);
      console.log('subscription_id:', session.subscription_id);
      console.log('payment_id:', session.payment_id);

      setResult(`✅ Premium Plan subscription created!\n\nSubscription ID: ${session.subscription_id || session.id}\nAmount: $10.00/month\nPayment Link: ${session.payment_link || session.checkout_url || session.url}\n\nOpening checkout...`);

      // Redirect to the checkout URL directly (same window)
      const redirectUrl = session.payment_link || session.checkout_url || session.url;
      console.log('Redirect URL:', redirectUrl);

      if (redirectUrl) {
        console.log('Setting timeout to redirect to:', redirectUrl);
        setTimeout(() => {
          console.log('Redirecting now to:', redirectUrl);
          window.location.href = redirectUrl;
        }, 1500);
      } else {
        console.error('No redirect URL found in session:', session);
        setResult(`❌ No checkout URL found in response. Check console for details.`);
      }
    } catch (error) {
      console.error('Direct subscription error:', error);
      setResult(`❌ Subscription Error: ${error instanceof Error ? error.message : 'Unknown error occurred'}`);
    } finally {
      setIsLoading(false);
    }
  };

  // Manual payment completion
  const handleManualPaymentCompletion = async () => {
    setIsLoading(true);
    setResult('');

    try {
      // Extract payment details from the last successful payment
      const paymentData = {
        payment_id: 'pay_RxVdEgRe0pREqEoZgab8q', // From the last payment
        subscription_id: 'sub_iEZsZXO4AYtVLGyDFYZqp', // From the last payment
        user_id: profile?.id,
        profile_id: profile?.id,
        amount: 1000 // $10.00 in cents
      };

      console.log('Manually completing payment with data:', paymentData);

      const response = await fetch('/api/payments/test/complete-payment', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(paymentData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Manual payment completion failed');
      }

      const result = await response.json();

      setResult(`✅ Payment completed manually!\n\nPayment ID: ${paymentData.payment_id}\nSubscription ID: ${paymentData.subscription_id}\nCredits Added: ${result.credits_added_amount}\nSubscription Updated: ${result.subscription_updated}\n\nResponse: ${JSON.stringify(result, null, 2)}`);

    } catch (error) {
      console.error('Manual payment completion error:', error);
      setResult(`❌ Manual Payment Completion Error: ${error instanceof Error ? error.message : 'Unknown error occurred'}`);
    } finally {
      setIsLoading(false);
    }
  };

  // Test webhook simulation
  const handleWebhookSimulation = async () => {
    setIsLoading(true);
    setResult('');

    try {
      const webhookData = {
        user_id: profile?.id,
        profile_id: profile?.id,
        amount: 1000, // $10.00 in cents
        event_type: 'payment.succeeded'
      };

      console.log('Simulating webhook with data:', webhookData);

      const response = await fetch('/api/payments/test/webhook', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(webhookData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Webhook simulation failed');
      }

      const result = await response.json();

      setResult(`✅ Webhook simulation successful!\n\nEvent: ${webhookData.event_type}\nAmount: $${webhookData.amount/100}\nUser ID: ${webhookData.user_id}\n\nResponse: ${JSON.stringify(result, null, 2)}`);

    } catch (error) {
      console.error('Webhook simulation error:', error);
      setResult(`❌ Webhook Simulation Error: ${error instanceof Error ? error.message : 'Unknown error occurred'}`);
    } finally {
      setIsLoading(false);
    }
  };

  // Test customer creation
  const handleCreateCustomer = async () => {
    setIsLoading(true);
    setResult('');
    
    try {
      if (!email) {
        throw new Error('Email is required to create a customer');
      }

      const customerData = {
        email,
        name: name || '',
        metadata: {
          test: true,
          created_from: 'consolidated_test_page'
        }
      };

      const response = await fetch('/api/payments/customers', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(customerData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create customer');
      }

      const customer = await response.json();
      
      setResult(`✅ Customer created!\n\nCustomer ID: ${customer.id}\nEmail: ${customer.email}\nName: ${customer.name}`);
      setCustomerId(customer.id);
    } catch (error) {
      console.error('Customer creation error:', error);
      setResult(`❌ Customer Error: ${error instanceof Error ? error.message : 'Unknown error occurred'}`);
    } finally {
      setIsLoading(false);
    }
  };

  // Clear authentication and refresh session
  const handleClearAuth = async () => {
    setIsLoading(true);
    setResult('');
    
    try {
      // Clear any cached session data
      await supabase.auth.signOut();
      
      // Clear localStorage auth data
      localStorage.removeItem('supabase.auth.token');
      localStorage.removeItem('sb-alywdwwqrtddplqsbksd-auth-token');
      localStorage.removeItem('sb-aibdxsebwhalbnugsqel-auth-token');
      
      setResult('🔄 Authentication cleared. Please refresh the page and sign in again.');
      
      // Redirect to auth page after a delay
      setTimeout(() => {
        window.location.href = '/auth';
      }, 2000);
    } catch (error) {
      console.error('Clear auth error:', error);
      setResult(`❌ Clear Auth Error: ${error instanceof Error ? error.message : 'Unknown error occurred'}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-6 max-w-6xl">
      <div className="mb-8">
        <h1 className="text-4xl font-bold mb-2">
          🦤 Dodo Payments Test Center
        </h1>
        <p className="text-muted-foreground">
          Comprehensive testing for Dodo Payments integration - API, SDK, and Subscriptions
        </p>
        <div className="flex gap-2 mt-2">
          <Badge variant="secondary">Test Mode</Badge>
          <Badge variant="outline">API + SDK</Badge>
          <Badge variant="outline">Authenticated</Badge>
        </div>
      </div>

      <Tabs defaultValue="payments" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="payments">Payments</TabsTrigger>
          <TabsTrigger value="subscriptions">Subscriptions</TabsTrigger>
          <TabsTrigger value="customers">Customers</TabsTrigger>
          <TabsTrigger value="info">Test Info</TabsTrigger>
        </TabsList>

        <TabsContent value="payments" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            {/* API Payment */}
            <Card>
              <CardHeader>
                <CardTitle>🔗 API Payment (Server-side)</CardTitle>
                <CardDescription>
                  Test payment via server API endpoint
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="api-amount">Amount</Label>
                  <Input
                    id="api-amount"
                    type="number"
                    step="0.01"
                    value={amount}
                    onChange={(e) => setAmount(e.target.value)}
                    placeholder="10.00"
                  />
                </div>
                <div>
                  <Label htmlFor="api-currency">Currency</Label>
                  <Input
                    id="api-currency"
                    value={currency}
                    onChange={(e) => setCurrency(e.target.value)}
                    placeholder="USD"
                  />
                </div>
                <Button 
                  onClick={handleAPIPayment}
                  disabled={isLoading || !amount}
                  className="w-full"
                >
                  {isLoading ? 'Processing...' : `Pay ${currency} ${amount} (API)`}
                </Button>
                <Button 
                  onClick={handleTestEndpoint}
                  disabled={isLoading}
                  variant="secondary"
                  className="w-full"
                >
                  {isLoading ? 'Processing...' : 'Test Simple Payment Endpoint'}
                </Button>
              </CardContent>
            </Card>

            {/* SDK Payment */}
            <Card>
              <CardHeader>
                <CardTitle>⚡ SDK Payment (Client-side)</CardTitle>
                <CardDescription>
                  Test payment via Dodo SDK overlay
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label>Amount: {currency} {amount}</Label>
                  <p className="text-sm text-muted-foreground">
                    Uses same amount from API payment
                  </p>
                </div>
                <Button 
                  onClick={handleSDKPayment}
                  disabled={isLoading || !amount}
                  className="w-full"
                  variant="outline"
                >
                  {isLoading ? 'Opening...' : `Pay ${currency} ${amount} (SDK)`}
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="subscriptions" className="space-y-6">
          {/* Subscription Plans Section */}
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold mb-2">Choose Your Plan</h2>
            <p className="text-muted-foreground">Select the perfect plan for your needs</p>
          </div>

          {/* Pricing Cards */}
          <div className="grid gap-6 md:grid-cols-1 lg:grid-cols-1 max-w-md mx-auto">
            <Card className="relative border-2 border-primary shadow-lg">
              <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                <Badge className="bg-primary text-primary-foreground px-3 py-1">
                  Test Subscription
                </Badge>
              </div>
              <CardHeader className="text-center pt-8">
                <CardTitle className="text-2xl">Premium Plan</CardTitle>
                <CardDescription>Perfect for testing subscription payments</CardDescription>
                <div className="mt-4">
                  <span className="text-4xl font-bold">$10</span>
                  <span className="text-muted-foreground">/month</span>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex items-center gap-3">
                    <div className="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center">
                      <span className="text-white text-xs">✓</span>
                    </div>
                    <span className="text-sm">Unlimited API calls</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center">
                      <span className="text-white text-xs">✓</span>
                    </div>
                    <span className="text-sm">Premium support</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center">
                      <span className="text-white text-xs">✓</span>
                    </div>
                    <span className="text-sm">Advanced features</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center">
                      <span className="text-white text-xs">✓</span>
                    </div>
                    <span className="text-sm">Cancel anytime</span>
                  </div>
                </div>

                <Separator />

                <Button
                  onClick={() => handleDirectSubscribe()}
                  disabled={isLoading}
                  className="w-full bg-primary hover:bg-primary/90 text-white font-semibold py-3"
                  size="lg"
                >
                  {isLoading ? (
                    <div className="flex items-center gap-2">
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                      Processing...
                    </div>
                  ) : (
                    'Subscribe Now'
                  )}
                </Button>

                <p className="text-xs text-muted-foreground text-center">
                  Secure payment powered by Dodo Payments
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Advanced Subscription Testing */}
          <Card className="mt-8">
            <CardHeader>
              <CardTitle>🔧 Advanced Subscription Testing</CardTitle>
              <CardDescription>
                Custom subscription parameters for testing
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <div>
                  <Label htmlFor="interval">Billing Interval</Label>
                  <select
                    id="interval"
                    value={interval}
                    onChange={(e) => setInterval(e.target.value as 'monthly' | 'yearly')}
                    className="w-full p-2 border rounded"
                  >
                    <option value="monthly">Monthly ($29.99)</option>
                    <option value="yearly">Yearly ($299.99)</option>
                  </select>
                </div>
                <div>
                  <Label htmlFor="planId">Plan ID (Optional)</Label>
                  <Input
                    id="planId"
                    value={planId}
                    onChange={(e) => setPlanId(e.target.value)}
                    placeholder="Auto-generated if empty"
                  />
                </div>
              </div>
              <Button
                onClick={handleSubscriptionPayment}
                disabled={isLoading}
                className="w-full"
                variant="outline"
              >
                {isLoading ? 'Processing...' : `Subscribe ${interval === 'monthly' ? 'Monthly' : 'Yearly'} (Custom)`}
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="customers" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>👤 Customer Management</CardTitle>
              <CardDescription>
                Create and manage test customers
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <div>
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder="<EMAIL>"
                  />
                </div>
                <div>
                  <Label htmlFor="name">Name</Label>
                  <Input
                    id="name"
                    value={name}
                    onChange={(e) => setName(e.target.value)}
                    placeholder="Test User"
                  />
                </div>
              </div>
              <div>
                <Label htmlFor="customerId">Customer ID</Label>
                <Input
                  id="customerId"
                  value={customerId}
                  onChange={(e) => setCustomerId(e.target.value)}
                  placeholder="Will be auto-filled after creation"
                  readOnly={!customerId}
                />
              </div>
              <Button 
                onClick={handleCreateCustomer}
                disabled={isLoading || !email || !name}
                className="w-full"
              >
                {isLoading ? 'Creating...' : 'Create Customer'}
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="info" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            {/* Test Cards */}
            <Card>
              <CardHeader>
                <CardTitle>💳 Test Cards</CardTitle>
                <CardDescription>
                  Use these test card numbers
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 text-sm font-mono">
                  <div><strong>Success:</strong> 4242 4242 4242 4242</div>
                  <div><strong>Decline:</strong> 4000 0000 0000 0002</div>
                  <div><strong>3D Secure:</strong> 4000 0000 0000 3220</div>
                  <div><strong>Insufficient:</strong> 4000 0000 0000 9995</div>
                  <div className="text-muted-foreground mt-2 font-sans">
                    Use any future expiry date and any 3-digit CVC
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Test Flow */}
            <Card>
              <CardHeader>
                <CardTitle>🧪 Test Flow</CardTitle>
                <CardDescription>
                  Recommended testing sequence
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ol className="list-decimal list-inside space-y-1 text-sm">
                  <li>Create a test customer</li>
                  <li>Test API payment (server-side)</li>
                  <li>Test SDK payment (client-side)</li>
                  <li>Test subscription payment</li>
                  <li>Use different test cards</li>
                  <li>Check webhook events</li>
                </ol>
              </CardContent>
            </Card>

            {/* Manual Payment Completion */}
            <Card>
              <CardHeader>
                <CardTitle>✅ Manual Payment Completion</CardTitle>
                <CardDescription>
                  Manually complete payment and update user credits/subscription
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-sm text-muted-foreground">
                  After completing a payment checkout, use this to manually process the payment completion (since webhooks don't work in local development).
                </p>
                <Button
                  onClick={handleManualPaymentCompletion}
                  disabled={isLoading || !profile}
                  className="w-full"
                  variant="default"
                >
                  {isLoading ? (
                    <div className="flex items-center gap-2">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      Completing Payment...
                    </div>
                  ) : (
                    '✅ Complete Last Payment Manually'
                  )}
                </Button>
                {!profile && (
                  <p className="text-xs text-red-500">Please log in to complete payment</p>
                )}
              </CardContent>
            </Card>

            {/* Webhook Simulation */}
            <Card>
              <CardHeader>
                <CardTitle>🔗 Webhook Simulation</CardTitle>
                <CardDescription>
                  Test webhook processing and user credit updates
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-sm text-muted-foreground">
                  Simulate a successful payment webhook to test user credit updates and subscription processing.
                </p>
                <Button
                  onClick={handleWebhookSimulation}
                  disabled={isLoading || !profile}
                  className="w-full"
                  variant="secondary"
                >
                  {isLoading ? (
                    <div className="flex items-center gap-2">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      Simulating Webhook...
                    </div>
                  ) : (
                    '🔗 Simulate Payment Webhook'
                  )}
                </Button>
                {!profile && (
                  <p className="text-xs text-red-500">Please log in to test webhook simulation</p>
                )}
              </CardContent>
            </Card>

            {/* Auth Troubleshooting */}
            <Card>
              <CardHeader>
                <CardTitle>🔐 Auth Troubleshooting</CardTitle>
                <CardDescription>
                  Fix authentication issues
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-sm text-muted-foreground">
                  If you're getting 400 Bad Request errors, try clearing your authentication session.
                </p>
                <Button 
                  onClick={handleClearAuth}
                  disabled={isLoading}
                  variant="destructive"
                  className="w-full"
                >
                  {isLoading ? 'Clearing...' : '🔄 Clear Auth & Re-login'}
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      <Separator className="my-8" />

      {/* Results */}
      <Card>
        <CardHeader>
          <CardTitle>📊 Test Results</CardTitle>
          <CardDescription>
            API responses and status updates
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Textarea
            value={result}
            readOnly
            placeholder="Test results will appear here...\n\n• API responses\n• Payment status\n• Error messages\n• Success confirmations"
            className="min-h-[200px] font-mono text-sm"
          />
        </CardContent>
      </Card>

      <div className="mt-8 p-4 bg-muted rounded-lg">
        <h3 className="font-semibold mb-2">🔧 Configuration Status:</h3>
        <div className="grid gap-2 text-sm">
          <div>API Key: {import.meta.env.VITE_DODO_PAYMENTS_API_KEY ? '✅ Configured' : '❌ Missing'}</div>
          <div>API URL: {import.meta.env.VITE_DODO_PAYMENTS_API_URL ? '✅ Configured' : '❌ Missing'}</div>
          <div>Mode: {import.meta.env.VITE_DODO_PAYMENTS_MODE || 'test'}</div>
          <div>User: {profile?.email || 'Not logged in'}</div>
        </div>
      </div>
    </div>
  );
}
