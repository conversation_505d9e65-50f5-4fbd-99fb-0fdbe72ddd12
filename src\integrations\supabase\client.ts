// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://alywdwwqrtddplqsbksd.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFseXdkd3dxcnRkZHBscXNia3NkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQwMjQyNTIsImV4cCI6MjA0OTYwMDI1Mn0.kiuDTgrGVi4rbZ3XYSIfqTTsiNUCvByDo5aDuXkwsZQ";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

const isDevelopment = import.meta.env.DEV;
const SITE_URL = isDevelopment 
  ? 'http://localhost:5174'  // Local development
  : 'https://pixelkeywording.com'; // Production

console.log('Current environment:', isDevelopment ? 'Development' : 'Production');
console.log('Site URL:', SITE_URL);

// Clear all auth sessions to fix authentication issues
if (typeof window !== 'undefined') {
  console.log('Clearing all auth sessions to fix authentication issues');

  // Clear all auth-related keys from localStorage
  Object.keys(localStorage).forEach(key => {
    if (key.includes('supabase') ||
        key.includes('auth') ||
        key.includes('aibdxsebwhalbnugsqel') ||
        key.includes('alywdwwqrtddplqsbksd') ||
        key.includes('pixelkeywording')) {
      console.log('Removing auth key:', key);
      localStorage.removeItem(key);
    }
  });

  // Clear all auth-related keys from sessionStorage
  Object.keys(sessionStorage).forEach(key => {
    if (key.includes('supabase') ||
        key.includes('auth') ||
        key.includes('aibdxsebwhalbnugsqel') ||
        key.includes('alywdwwqrtddplqsbksd') ||
        key.includes('pixelkeywording')) {
      console.log('Removing session key:', key);
      sessionStorage.removeItem(key);
    }
  });

  // Force clear any remaining auth cookies
  document.cookie.split(";").forEach(function(c) {
    document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/");
  });
}

// Function to get the current origin, handling both development and production
const getCurrentOrigin = () => {
  if (typeof window === 'undefined') return SITE_URL;
  // Always return the main domain in production to avoid subdomain issues
  if (!isDevelopment) return SITE_URL;
  return window.location.origin;
};

// Create Supabase client with auth logging disabled
export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    persistSession: true,
    autoRefreshToken: true,
    detectSessionInUrl: true,
    flowType: 'pkce',
    storageKey: 'pixelkeywording-auth-v3',
    storage: {
      getItem: (key) => {
        try {
          if (typeof window === 'undefined') return null;
          const storedSession = localStorage.getItem(key);
          console.log(`Getting auth item ${key}:`, storedSession ? 'found' : 'not found');
          return storedSession;
        } catch (error) {
          console.error('Error getting auth session from storage:', error);
          return null;
        }
      },
      setItem: (key, value) => {
        try {
          if (typeof window === 'undefined') return;
          localStorage.setItem(key, value);
          console.log(`Setting auth item ${key}:`, 'success');
        } catch (error) {
          console.error('Error setting auth session to storage:', error);
        }
      },
      removeItem: (key) => {
        try {
          if (typeof window === 'undefined') return;
          localStorage.removeItem(key);
          console.log(`Removing auth item ${key}:`, 'success');
        } catch (error) {
          console.error('Error removing auth session from storage:', error);
        }
      },
    },
    debug: false // Disable auth debug logging
  }
});